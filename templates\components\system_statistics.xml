<?xml version="1.0" encoding="utf-8"?>
<templates>
    <!-- System Statistics Dashboard Component -->
    <t t-name="components.system_statistics">
        <div class="bg-white border border-gray-200 rounded-lg p-4 sm:p-6 mb-6 shadow-sm">
            <!-- Header -->
            <div class="flex items-center justify-between mb-6">
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                        <i class="fas fa-chart-line text-blue-600 mr-2"></i>
                        System Statistics
                    </h3>
                    <p class="text-sm text-gray-500 mt-1">Real-time system and database metrics</p>
                </div>
                <button class="inline-flex items-center px-3 py-1.5 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-1 focus:ring-blue-500 transition-colors"
                        onclick="refreshSystemStats()">
                    <i class="fas fa-sync-alt mr-1.5 text-xs"></i>
                    Refresh
                </button>
            </div>

            <!-- Statistics Grid -->
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                <!-- Total Databases -->
                <div class="bg-gradient-to-br from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-blue-600">Total Databases</p>
                            <p class="text-2xl font-bold text-blue-900" id="total-databases">
                                <span t-esc="databases and databases.__len__() or 0"/>
                            </p>
                        </div>
                        <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-database text-blue-600"></i>
                        </div>
                    </div>
                </div>

                <!-- Active Connections -->
                <div class="bg-gradient-to-br from-green-50 to-emerald-50 border border-green-200 rounded-lg p-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-green-600">Active Connections</p>
                            <p class="text-2xl font-bold text-green-900" id="total-connections">
                                <span t-esc="sum(db.get('active_connections', 0) for db in databases) if databases else 0"/>
                            </p>
                        </div>
                        <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-plug text-green-600"></i>
                        </div>
                    </div>
                </div>

                <!-- Total Tables -->
                <div class="bg-gradient-to-br from-purple-50 to-violet-50 border border-purple-200 rounded-lg p-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-purple-600">Total Tables</p>
                            <p class="text-2xl font-bold text-purple-900" id="total-tables">
                                <span t-esc="sum(db.get('table_count', 0) for db in databases) if databases else 0"/>
                            </p>
                        </div>
                        <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-table text-purple-600"></i>
                        </div>
                    </div>
                </div>

                <!-- Memory Registries -->
                <div class="bg-gradient-to-br from-orange-50 to-amber-50 border border-orange-200 rounded-lg p-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-orange-600">Memory Registries</p>
                            <p class="text-2xl font-bold text-orange-900" id="memory-registries">
                                <span t-esc="sum(1 for db in databases if db.get('has_memory_registry', False)) if databases else 0"/>
                            </p>
                        </div>
                        <div class="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-memory text-orange-600"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Database Health Overview -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Database Status Distribution -->
                <div class="bg-gray-50 rounded-lg p-4">
                    <h4 class="text-sm font-semibold text-gray-900 mb-3 flex items-center">
                        <i class="fas fa-heartbeat text-red-500 mr-2"></i>
                        Database Health
                    </h4>
                    <div class="space-y-3">
                        <t t-set="initialized_count" t-value="sum(1 for db in databases if db.get('is_initialized', False)) if databases else 0"/>
                        <t t-set="uninitialized_count" t-value="(databases and databases.__len__() - initialized_count) if databases else 0"/>
                        
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-2">
                                <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                                <span class="text-sm text-gray-700">Initialized</span>
                            </div>
                            <span class="text-sm font-medium text-gray-900" t-esc="initialized_count"/>
                        </div>
                        
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-2">
                                <div class="w-3 h-3 bg-yellow-500 rounded-full"></div>
                                <span class="text-sm text-gray-700">Not Initialized</span>
                            </div>
                            <span class="text-sm font-medium text-gray-900" t-esc="uninitialized_count"/>
                        </div>
                        
                        <!-- Progress Bar -->
                        <div class="mt-3">
                            <div class="flex justify-between text-xs text-gray-600 mb-1">
                                <span>Initialization Progress</span>
                                <span t-esc="str(round((initialized_count / databases.__len__() * 100) if databases and databases.__len__() > 0 else 0, 1)) + '%'"/>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-green-500 h-2 rounded-full transition-all duration-300" 
                                     t-att-style="'width: ' + str((initialized_count / databases.__len__() * 100) if databases and databases.__len__() > 0 else 0) + '%'"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- System Information -->
                <div class="bg-gray-50 rounded-lg p-4">
                    <h4 class="text-sm font-semibold text-gray-900 mb-3 flex items-center">
                        <i class="fas fa-server text-blue-500 mr-2"></i>
                        System Information
                    </h4>
                    <div class="space-y-3 text-sm">
                        <div class="flex justify-between">
                            <span class="text-gray-600">Mode:</span>
                            <span class="font-medium text-gray-900" t-esc="'Multi-DB' if config.is_multi_db_mode else 'Single-DB'"/>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Host:</span>
                            <span class="font-medium text-gray-900" t-esc="config.get('host', 'localhost')"/>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Port:</span>
                            <span class="font-medium text-gray-900" t-esc="config.get('port', '8000')"/>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Database Filter:</span>
                            <span class="font-medium text-gray-900 text-xs font-mono bg-gray-200 px-2 py-1 rounded" t-esc="config.get('db_filter', 'None')"/>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="mt-6 pt-4 border-t border-gray-200">
                <h4 class="text-sm font-semibold text-gray-900 mb-3 flex items-center">
                    <i class="fas fa-bolt text-yellow-500 mr-2"></i>
                    Quick Actions
                </h4>
                <div class="flex flex-wrap gap-2">
                    <button class="inline-flex items-center px-3 py-1.5 bg-blue-100 text-blue-700 rounded-md text-xs font-medium hover:bg-blue-200 transition-colors"
                            onclick="showCreateDatabaseModal()">
                        <i class="fas fa-plus mr-1"></i>
                        Create Database
                    </button>
                    <button class="inline-flex items-center px-3 py-1.5 bg-green-100 text-green-700 rounded-md text-xs font-medium hover:bg-green-200 transition-colors"
                            onclick="refreshSystemStats()">
                        <i class="fas fa-sync-alt mr-1"></i>
                        Refresh Stats
                    </button>
                    <button class="inline-flex items-center px-3 py-1.5 bg-purple-100 text-purple-700 rounded-md text-xs font-medium hover:bg-purple-200 transition-colors"
                            onclick="exportSystemReport()">
                        <i class="fas fa-file-export mr-1"></i>
                        Export Report
                    </button>
                </div>
            </div>
        </div>
    </t>
</templates>
