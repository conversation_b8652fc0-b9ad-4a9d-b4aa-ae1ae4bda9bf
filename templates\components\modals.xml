<?xml version="1.0" encoding="utf-8"?>
<templates>
    <!-- Database Info Modal Component -->
    <t t-name="components.database_info_modal">
        <div id="databaseInfoModal" class="hidden fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm z-50 items-center justify-center p-4">
            <div class="bg-white rounded-xl shadow-2xl max-w-lg w-full max-h-[90vh] overflow-y-auto animate-slide-up">
                <!-- Modal Header -->
                <div class="flex items-center justify-between p-6 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900 flex items-center space-x-2">
                        <i class="fas fa-info-circle text-blue-600"></i>
                        <span>Database Information</span>
                    </h3>
                    <button type="button" 
                            class="text-gray-400 hover:text-gray-600 transition-colors focus:outline-none focus:ring-1 focus:ring-blue-500 rounded-md p-1" 
                            onclick="hideDatabaseInfoModal()">
                        <i class="fas fa-times text-lg"></i>
                    </button>
                </div>
                
                <!-- Modal Body -->
                <div class="p-6">
                    <div id="databaseInfoContent">
                        <!-- Content will be populated dynamically -->
                        <div class="text-center text-gray-500">
                            <i class="fas fa-spinner fa-spin text-2xl mb-2"></i>
                            <p>Loading database information...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </t>

    <!-- Generic Modal Base Component -->
    <t t-name="components.modal_base">
        <div t-att-id="modal_id or 'genericModal'" 
             t-att-class="'fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm z-50 items-center justify-center p-4 ' + (hidden and 'hidden' or '')">
            <div t-att-class="'bg-white rounded-xl shadow-2xl w-full max-h-[90vh] overflow-y-auto animate-slide-up ' + (modal_size or 'max-w-md')">
                <!-- Modal Header -->
                <div class="flex items-center justify-between p-6 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900 flex items-center space-x-2">
                        <i t-att-class="'fas ' + (header_icon or 'fa-info-circle') + ' text-blue-600'"></i>
                        <span t-esc="header_title or 'Modal'"/>
                    </h3>
                    <button type="button" 
                            class="text-gray-400 hover:text-gray-600 transition-colors focus:outline-none focus:ring-1 focus:ring-blue-500 rounded-md p-1" 
                            t-att-onclick="close_function or 'closeModal()'">
                        <i class="fas fa-times text-lg"></i>
                    </button>
                </div>
                
                <!-- Modal Body -->
                <div class="p-6">
                    <t t-if="modal_content">
                        <t t-raw="modal_content"/>
                    </t>
                    <t t-else="">
                        <t t-slot="modal_body"/>
                    </t>
                </div>
                
                <!-- Modal Footer (optional) -->
                <t t-if="modal_footer or has_footer">
                    <div class="flex items-center justify-end space-x-3 p-6 border-t border-gray-200">
                        <t t-if="modal_footer">
                            <t t-raw="modal_footer"/>
                        </t>
                        <t t-else="">
                            <t t-slot="modal_footer"/>
                        </t>
                    </div>
                </t>
            </div>
        </div>
    </t>
</templates>
