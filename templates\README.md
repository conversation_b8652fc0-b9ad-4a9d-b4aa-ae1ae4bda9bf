# Templates Directory Structure

This directory contains all XML templates for the ERP system, organized in a modular structure for better maintainability and reusability.

## Directory Structure

```
templates/
├── layouts/
│   └── base_layout.xml          # Main layout template
├── styles/
│   └── tailwind_config.xml      # CSS configuration and custom styles
├── components/
│   ├── database_card.xml        # Database card component
│   ├── create_database_modal.xml # Create database modal
│   ├── modals.xml              # Generic modal components
│   ├── empty_state.xml         # Empty/loading/error state components
│   ├── status_bar.xml          # Status bar and badge components
│   ├── system_statistics.xml   # System statistics dashboard
│   └── ui_utilities.xml        # Reusable UI components (buttons, badges, forms)
├── database_list_enhanced_modular.xml # Main database list page
├── error.xml                   # Error page template
└── README.md                   # This documentation file
```

## Template Dependencies

### Loading Order
Templates should be loaded in the following order to ensure proper dependencies:

1. **Base Templates**
   - `layouts/base_layout.xml`
   - `styles/tailwind_config.xml`

2. **Utility Components**
   - `components/ui_utilities.xml`
   - `components/status_bar.xml` (includes status_badge)

3. **UI Components**
   - `components/empty_state.xml`
   - `components/modals.xml`
   - `components/create_database_modal.xml`
   - `components/database_card.xml`
   - `components/system_statistics.xml`

4. **Page Templates**
   - `database_list_enhanced_modular.xml`
   - `error.xml`

### Component Dependencies

#### base_layout.xml
- **Provides**: Main layout structure, header, footer
- **Depends on**: `styles.tailwind_config`
- **Used by**: All page templates

#### status_bar.xml
- **Provides**: 
  - `components.status_bar`
  - `components.system_mode_badge`
  - `components.database_count_badge`
  - `components.environment_info`
  - `components.status_badge` (moved from database_card.xml)
- **Depends on**: None
- **Used by**: `database_list_enhanced_modular.xml`, `database_card.xml`

#### database_card.xml
- **Provides**: `components.database_card`
- **Depends on**: `components.status_badge` (from status_bar.xml)
- **Used by**: `database_list_enhanced_modular.xml`

#### modals.xml
- **Provides**: 
  - `components.database_info_modal`
  - `components.modal_base` (generic modal template)
- **Depends on**: None
- **Used by**: `database_list_enhanced_modular.xml`

#### ui_utilities.xml
- **Provides**: Reusable UI components:
  - Button variants (`button_primary`, `button_secondary`, `button_danger`)
  - Badge variants (`badge_success`, `badge_warning`, `badge_error`, `badge_info`)
  - Form components (`form_input`, `form_select`)
  - Utility components (`loading_spinner`, `card`, `alert`)
- **Depends on**: None
- **Used by**: Can be used by any template needing standardized UI components

## Template Usage Examples

### Using UI Utilities

```xml
<!-- Primary button -->
<t t-call="components.button_primary">
    <t t-set="button_text" t-value="'Save'"/>
    <t t-set="icon" t-value="'fa-save'"/>
    <t t-set="onclick_handler" t-value="'saveData()'"/>
</t>

<!-- Success badge -->
<t t-call="components.badge_success">
    <t t-set="badge_text" t-value="'Active'"/>
    <t t-set="icon" t-value="'fa-check'"/>
</t>

<!-- Form input -->
<t t-call="components.form_input">
    <t t-set="label" t-value="'Database Name'"/>
    <t t-set="input_id" t-value="'dbName'"/>
    <t t-set="input_name" t-value="'name'"/>
    <t t-set="required" t-value="True"/>
    <t t-set="placeholder" t-value="'Enter database name'"/>
</t>
```

### Using Generic Modal

```xml
<t t-call="components.modal_base">
    <t t-set="modal_id" t-value="'myModal'"/>
    <t t-set="header_title" t-value="'Confirmation'"/>
    <t t-set="header_icon" t-value="'fa-question-circle'"/>
    <t t-set="modal_size" t-value="'max-w-sm'"/>
    <t t-set="close_function" t-value="'closeMyModal()'"/>
    <t t-slot="modal_body">
        <p>Are you sure you want to proceed?</p>
    </t>
    <t t-slot="modal_footer">
        <button onclick="closeMyModal()">Cancel</button>
        <button onclick="confirmAction()">Confirm</button>
    </t>
</t>
```

## Refactoring Changes Made

### 1. Removed Duplicates
- Removed duplicate `status_badge` component from `database_card.xml`
- Consolidated status-related components in `status_bar.xml`

### 2. Extracted Components
- Moved `database_info_modal` from `create_database_modal.xml` to `modals.xml`
- Created generic `modal_base` component for reusability

### 3. Added Utilities
- Created `ui_utilities.xml` with standardized UI components
- Provides consistent styling and behavior across the application

### 4. Improved Organization
- Better separation of concerns
- Clearer dependency hierarchy
- Removed unused test template

## Best Practices

1. **Component Naming**: Use descriptive names with namespace prefixes (e.g., `components.button_primary`)
2. **Dependencies**: Keep dependencies minimal and well-documented
3. **Reusability**: Create generic components that can be customized through parameters
4. **Loading Order**: Load base templates before dependent components
5. **Documentation**: Update this README when adding new templates or changing dependencies
