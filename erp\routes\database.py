"""
Database list viewing routes - only list viewing and redirection
"""
from fastapi import API<PERSON><PERSON><PERSON>, Request, HTTPException
from fastapi.responses import HTMLResponse
from typing import Dict, Any, List
import re

from ..config import config
from ..database.registry import DatabaseRegistry
from ..templates.manager import get_template_manager

# Only view router for database listing - no API management routes
view_router = APIRouter(prefix="/databases", tags=["database-views"])


@view_router.get("/test")
async def test_route():
    """Test route to verify router is working"""
    return {"status": "success", "message": "Database router is working", "route": "/databases/test"}


async def get_database_list() -> List[Dict[str, Any]]:
    """Helper function to get database list with information"""
    from ..database.memory import DatabaseFilterProcessor, MemoryRegistryManager
    
    db_info_list = []

    # Get all available databases
    databases = await DatabaseRegistry.list_databases()
    
    # Process database filtering using the new system
    filter_result = await DatabaseFilterProcessor.process_database_filter(
        databases=databases,
        db_filter=config.db_filter,
        specific_db_name=config.get_default_database()
    )
    
    filtered_databases = filter_result['filtered_databases']
    
    # If memory registry should be created, ensure it exists
    if filter_result['should_create_registry'] and filter_result['registry_db_name']:
        registry_db = filter_result['registry_db_name']
        await MemoryRegistryManager.get_registry(registry_db)
        print(f"[INFO] Memory registry ensured for database: {registry_db}")

    # Build database info for filtered databases
    for db_name in filtered_databases:
        # Check if this database has a memory registry
        has_registry = await MemoryRegistryManager.has_registry(db_name)
        
        # Check if base module is installed
        base_module_installed = await MemoryRegistryManager._is_base_module_installed(db_name)
        
        # Determine database initialization status
        if base_module_installed:
            init_status = "ready"
            init_message = "Base module installed - Ready for use"
            init_icon = "fa-check-circle"
            init_color = "green"
        else:
            init_status = "needs_init"
            init_message = "Requires base module installation"
            init_icon = "fa-exclamation-triangle"
            init_color = "yellow"
        
        # Get comprehensive database information
        try:
            db_manager = await DatabaseRegistry.get_database('postgres')

            # Get database size
            size_query = f"SELECT pg_size_pretty(pg_database_size('{db_name}')) as size"
            size_result = await db_manager.fetch(size_query)
            size = size_result[0]['size'] if size_result else 'Unknown'

            # Get creation date (approximate)
            created_query = f"""
                SELECT (pg_stat_file('base/'||oid||'/PG_VERSION')).modification as created
                FROM pg_database WHERE datname = '{db_name}'
            """
            created_result = await db_manager.fetch(created_query)
            created = created_result[0]['created'].isoformat() if created_result else None

            # Get database statistics
            stats_query = f"""
                SELECT
                    d.datname as name,
                    pg_catalog.pg_get_userbyid(d.datdba) as owner,
                    pg_encoding_to_char(d.encoding) as encoding,
                    d.datcollate as collate,
                    d.datctype as ctype,
                    CASE WHEN d.datallowconn THEN 'Yes' ELSE 'No' END as allow_connections,
                    d.datconnlimit as connection_limit,
                    pg_size_pretty(pg_database_size(d.datname)) as size_pretty,
                    pg_database_size(d.datname) as size_bytes
                FROM pg_database d
                WHERE d.datname = '{db_name}'
            """
            stats_result = await db_manager.fetch(stats_query)
            db_stats = stats_result[0] if stats_result else {}

            # Get connection count
            conn_query = f"""
                SELECT count(*) as active_connections
                FROM pg_stat_activity
                WHERE datname = '{db_name}' AND state = 'active'
            """
            conn_result = await db_manager.fetch(conn_query)
            active_connections = conn_result[0]['active_connections'] if conn_result else 0

            # Get table count
            table_query = f"""
                SELECT count(*) as table_count
                FROM information_schema.tables
                WHERE table_catalog = '{db_name}'
                AND table_schema NOT IN ('information_schema', 'pg_catalog')
            """
            table_result = await db_manager.fetch(table_query)
            table_count = table_result[0]['table_count'] if table_result else 0

        except Exception as e:
            # Use logger for consistency
            from erp.logging import get_logger
            logger = get_logger(__name__)
            logger.debug(f"Error getting info for database {db_name}: {e}")
            size = 'Unknown'
            created = None
            db_stats = {}
            active_connections = 0
            table_count = 0

        # Format created date for display
        created_display = None
        if created:
            try:
                from datetime import datetime
                created_dt = datetime.fromisoformat(created.replace('Z', '+00:00'))
                created_display = created_dt.strftime('%Y-%m-%d %H:%M')
            except:
                created_display = created

        db_info_list.append({
            'name': db_name,
            'size': size,
            'created': created,
            'created_display': created_display,
            'owner': db_stats.get('owner', 'erp'),
            'encoding': db_stats.get('encoding', 'UTF8'),
            'collate': db_stats.get('collate', 'Unknown'),
            'ctype': db_stats.get('ctype', 'Unknown'),
            'allow_connections': db_stats.get('allow_connections', 'Yes'),
            'connection_limit': db_stats.get('connection_limit', -1),
            'size_bytes': db_stats.get('size_bytes', 0),
            'active_connections': active_connections,
            'table_count': table_count,
            'status': 'active',  # Default status
            'has_memory_registry': has_registry,
            'registry_info': filter_result if has_registry else None,
            # New initialization status fields
            'base_module_installed': base_module_installed,
            'is_initialized': base_module_installed,  # Template expects this field
            'init_status': init_status,
            'init_message': init_message,
            'init_icon': init_icon,
            'init_color': init_color
        })

    return db_info_list




async def load_all_templates(template_manager):
    """Load all templates from the templates directory in proper dependency order"""
    import os
    from pathlib import Path

    templates_dir = Path("templates")
    if not templates_dir.exists():
        raise Exception(f"Templates directory not found: {templates_dir}")

    # Define template loading order based on dependencies
    template_load_order = [
        # 1. Base Templates
        "layouts/base_layout.xml",
        "styles/tailwind_config.xml",

        # 2. Utility Components
        "components/ui_utilities.xml",
        "components/status_bar.xml",

        # 3. UI Components
        "components/empty_state.xml",
        "components/modals.xml",
        "components/create_database_modal.xml",
        "components/database_card.xml",
        "components/system_statistics.xml",

        # 4. Page Templates
        "database_list_enhanced_modular.xml",
        "error.xml"
    ]

    loaded_count = 0
    failed_templates = []

    # Load templates in dependency order
    for template_file in template_load_order:
        template_path = templates_dir / template_file
        if template_path.exists():
            try:
                template_manager.load_template_file(template_file)
                loaded_count += 1
                print(f"Loaded template: {template_file}")
            except Exception as e:
                failed_templates.append((template_file, str(e)))
                print(f"Warning: Failed to load template {template_file}: {e}")
        else:
            print(f"Warning: Template file not found: {template_file}")

    # Load any remaining templates not in the ordered list
    for root, dirs, files in os.walk(templates_dir):
        for file in files:
            if file.endswith('.xml'):
                rel_path = os.path.relpath(os.path.join(root, file), templates_dir)
                rel_path = rel_path.replace('\\', '/')  # Normalize path separators

                if rel_path not in template_load_order:
                    try:
                        template_manager.load_template_file(rel_path)
                        loaded_count += 1
                        print(f"Loaded additional template: {rel_path}")
                    except Exception as e:
                        failed_templates.append((rel_path, str(e)))
                        print(f"Warning: Failed to load additional template {rel_path}: {e}")

    print(f"Loaded {loaded_count} templates total")
    if failed_templates:
        print(f"Failed to load {len(failed_templates)} templates:")
        for template_file, error in failed_templates:
            print(f"  - {template_file}: {error}")

    return loaded_count


@view_router.get("/", response_class=HTMLResponse)
async def database_list_page(request: Request):
    """Database list page with full template system"""
    try:
        template_manager = get_template_manager()

        # Load all templates automatically
        try:
            loaded_count = await load_all_templates(template_manager)
            print(f"DEBUG: Loaded {loaded_count} templates")
            print(f"DEBUG: Available templates: {template_manager.list_templates()}")
        except Exception as e:
            import traceback
            return HTMLResponse(
                content=f"<html><body><h1>Template Loading Error</h1><p>{str(e)}</p><pre>{traceback.format_exc()}</pre></body></html>",
                status_code=500
            )

        # Get database list
        try:
            databases = await get_database_list()
        except Exception as e:
            return HTMLResponse(
                content=f"<html><body><h1>Database List Error</h1><p>{str(e)}</p></body></html>",
                status_code=500
            )

        # Template context for the enhanced modular template
        context = {
            "request": request,
            "title": "Database Management - ERP System",
            "header_title": "Database Management",
            "header_subtitle": "Manage your ERP databases",
            "databases": databases,
            "config": config,
            "is_development_mode": config.is_development_mode,
            "show_header": True,
            "show_footer": True
        }

        # Check if our template exists
        enhanced_exists = template_manager.template_exists("database_list_enhanced_modular")
        base_exists = template_manager.template_exists("base_layout")

        # Try to render the enhanced template
        try:
            print(f"DEBUG: Enhanced template exists: {enhanced_exists}")
            print(f"DEBUG: Base layout exists: {base_exists}")
            print(f"DEBUG: Database count: {len(databases) if databases else 0}")

            # For now, use base_layout directly since enhanced template inheritance has issues
            print("DEBUG: Using base_layout template with custom content")
            elif base_exists:
                print("DEBUG: Rendering base layout template")
                # Add simple content for base layout fallback
                context["content"] = f"""
                <div class="text-center py-12">
                    <h2 class="text-2xl font-bold text-gray-900 mb-4">Database Management</h2>
                    <p class="text-gray-600 mb-6">Found {len(databases) if databases else 0} database(s)</p>
                    {f'<div class="space-y-4">' + ''.join([f'<div class="bg-white p-4 rounded-lg shadow"><h3 class="font-semibold">{db["name"]}</h3><p class="text-sm text-gray-600">Size: {db.get("size", "Unknown")} | Tables: {db.get("table_count", 0)}</p></div>' for db in databases]) + '</div>' if databases else '<p class="text-gray-500">No databases available</p>'}
                </div>
                """
                rendered_content = template_manager.render_template("base_layout", context)
                print(f"DEBUG: Base layout rendered content length: {len(rendered_content)}")
                return HTMLResponse(
                    content=rendered_content,
                    status_code=200
                )
            else:
                print("DEBUG: No templates found, returning debug info")
                # Return debug information if templates are missing
                available_templates = template_manager.list_templates()
                return HTMLResponse(
                    content=f"""
                    <html><body>
                        <h1>Template Debug Information</h1>
                        <p><strong>Enhanced template exists:</strong> {enhanced_exists}</p>
                        <p><strong>Base layout exists:</strong> {base_exists}</p>
                        <p><strong>Available templates:</strong> {available_templates}</p>
                        <p><strong>Database count:</strong> {len(databases) if databases else 0}</p>
                        <p><strong>Config:</strong> {dict(config.__dict__) if hasattr(config, '__dict__') else str(config)}</p>
                    </body></html>
                    """,
                    status_code=200
                )
        except Exception as e:
            import traceback
            print(f"DEBUG: Template rendering error: {str(e)}")
            print(f"DEBUG: Traceback: {traceback.format_exc()}")
            return HTMLResponse(
                content=f"<html><body><h1>Template Rendering Error</h1><p><strong>Error:</strong> {str(e)}</p><p><strong>Traceback:</strong><pre>{traceback.format_exc()}</pre></p><p><strong>Available templates:</strong> {template_manager.list_templates()}</p></body></html>",
                status_code=500
            )

    except Exception as e:
        return HTMLResponse(
            content=f"<html><body><h1>General Error</h1><p>{str(e)}</p></body></html>",
            status_code=500
        )


