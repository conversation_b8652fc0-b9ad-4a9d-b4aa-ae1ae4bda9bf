<?xml version="1.0" encoding="utf-8"?>
<templates>
    <!-- Create Database Modal Component -->
    <t t-name="components.create_database_modal">
        <div id="createDatabaseModal" class="hidden fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm z-50 items-center justify-center p-4">
            <div class="bg-white rounded-xl shadow-2xl max-w-md w-full max-h-[90vh] overflow-y-auto animate-slide-up">
                <!-- Modal Header -->
                <div class="flex items-center justify-between p-6 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900 flex items-center space-x-2">
                        <i class="fas fa-plus-circle text-blue-600"></i>
                        <span>Create New Database</span>
                    </h3>
                    <button type="button" 
                            class="text-gray-400 hover:text-gray-600 transition-colors focus:outline-none focus:ring-1 focus:ring-blue-500 rounded-md p-1" 
                            onclick="hideCreateDatabaseModal()">
                        <i class="fas fa-times text-lg"></i>
                    </button>
                </div>
                
                <!-- Modal Body -->
                <div class="p-6">
                    <form id="createDatabaseForm" onsubmit="databaseOps.handleCreateDatabase(event)">
                        <!-- Database Name -->
                        <div class="mb-4">
                            <label for="dbName" class="block text-sm font-medium text-gray-700 mb-2">
                                Database Name <span class="text-red-500">*</span>
                            </label>
                            <input type="text"
                                   id="dbName"
                                   name="name"
                                   required="required"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 transition-colors text-sm"
                                   placeholder="Enter database name"
                                   pattern="[a-zA-Z0-9_-]+"
                                   title="Only letters, numbers, underscores, and hyphens are allowed" />
                            <p class="mt-1 text-xs text-gray-500">Only letters, numbers, underscores, and hyphens are allowed</p>
                        </div>
                        
                        <!-- Language Selection -->
                        <div class="mb-4">
                            <label for="dbLanguage" class="block text-sm font-medium text-gray-700 mb-2">
                                Language
                            </label>
                            <select id="dbLanguage" 
                                    name="language" 
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 transition-colors text-sm">
                                <option value="en_US">English (US)</option>
                                <option value="en_GB">English (UK)</option>
                                <option value="fr_FR">French</option>
                                <option value="de_DE">German</option>
                                <option value="es_ES">Spanish</option>
                                <option value="it_IT">Italian</option>
                                <option value="pt_PT">Portuguese</option>
                                <option value="nl_NL">Dutch</option>
                                <option value="ru_RU">Russian</option>
                                <option value="zh_CN">Chinese (Simplified)</option>
                                <option value="ja_JP">Japanese</option>
                                <option value="ko_KR">Korean</option>
                            </select>
                        </div>
                        
                        <!-- Demo Data -->
                        <div class="mb-6">
                            <div class="flex items-center">
                                <input type="checkbox"
                                       id="demoData"
                                       name="demo"
                                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" />
                                <label for="demoData" class="ml-2 block text-sm text-gray-700">
                                    Install demo data
                                </label>
                            </div>
                            <p class="mt-1 text-xs text-gray-500">Include sample data for testing and learning</p>
                        </div>
                        
                        <!-- Form Actions -->
                        <div class="flex items-center justify-end space-x-3">
                            <button type="button" 
                                    class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-1 focus:ring-blue-500 transition-colors" 
                                    onclick="hideCreateDatabaseModal()">
                                Cancel
                            </button>
                            <button type="submit" 
                                    class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-1 focus:ring-blue-500 transition-colors">
                                <i class="fas fa-plus mr-2"></i>
                                Create Database
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </t>
</templates>
