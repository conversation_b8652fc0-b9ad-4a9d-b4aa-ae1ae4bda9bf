#!/usr/bin/env python3
"""
Test the template system to see if it's working
"""
import asyncio
from erp.templates.manager import get_template_manager

async def test_template_system():
    """Test if the template system is working"""
    template_manager = get_template_manager()
    
    print("Template manager created")
    print(f"Template directories: {template_manager.template_dirs}")
    
    # Try to load a simple template
    try:
        print("Loading base_layout template...")
        template_manager.load_template_file("layouts/base_layout.xml")
        print("✅ base_layout loaded successfully")
    except Exception as e:
        print(f"❌ Failed to load base_layout: {e}")
        return
    
    # Load all required templates in dependency order
    template_files = [
        # Base templates
        "layouts/base_layout.xml",
        "styles/tailwind_config.xml",

        # Utility components
        "components/ui_utilities.xml",
        "components/status_bar.xml",

        # UI components
        "components/empty_state.xml",
        "components/modals.xml",
        "components/create_database_modal.xml",
        "components/database_card.xml",
        "components/system_statistics.xml",

        # Page templates
        "database_list_enhanced_modular.xml",
        "error.xml"
    ]

    for template_file in template_files:
        try:
            print(f"Loading {template_file}...")
            template_manager.load_template_file(template_file)
            print(f"✅ {template_file} loaded successfully")
        except Exception as e:
            print(f"❌ Failed to load {template_file}: {e}")
            return
    
    # List all loaded templates
    templates = template_manager.list_templates()
    print(f"All loaded templates: {templates}")

    # Test if key templates are loaded
    key_templates = ["base_layout", "database_list_enhanced_modular", "components.database_card"]
    for template_name in key_templates:
        if template_name in templates:
            print(f"✅ {template_name} is loaded")
        else:
            print(f"❌ {template_name} is missing")

    # Try to render the complex template
    try:
        context = {
            "title": "Test Database Management",
            "databases": [],
            "config": {
                "is_multi_db_mode": True,
                "host": "localhost",
                "port": "8000",
                "database_url": "postgresql://localhost/test"
            }
        }
        print("Attempting to render database list template...")
        result = template_manager.render_template("database_list_enhanced_modular", context)
        print(f"✅ Database list template rendered successfully, length: {len(result)}")
        if len(result) < 200:
            print(f"Result: {result}")
        else:
            print(f"Result preview: {result[:200]}...")
    except Exception as e:
        print(f"❌ Failed to render database list template: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_template_system())
