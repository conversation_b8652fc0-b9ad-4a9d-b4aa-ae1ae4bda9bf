<?xml version="1.0" encoding="utf-8"?>
<templates>
    <!-- Button Components -->
    <t t-name="components.button_primary">
        <button t-att-class="'inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-1 focus:ring-blue-500 transition-colors ' + (additional_classes or '')"
                t-att-onclick="onclick_handler"
                t-att-type="button_type or 'button'"
                t-att-disabled="disabled">
            <i t-if="icon" t-att-class="'fas ' + icon + ' mr-2'"></i>
            <span t-esc="button_text or 'Button'"/>
        </button>
    </t>

    <t t-name="components.button_secondary">
        <button t-att-class="'inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-1 focus:ring-blue-500 transition-colors ' + (additional_classes or '')"
                t-att-onclick="onclick_handler"
                t-att-type="button_type or 'button'"
                t-att-disabled="disabled">
            <i t-if="icon" t-att-class="'fas ' + icon + ' mr-2'"></i>
            <span t-esc="button_text or 'Button'"/>
        </button>
    </t>

    <t t-name="components.button_danger">
        <button t-att-class="'inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md hover:bg-red-700 focus:outline-none focus:ring-1 focus:ring-red-500 transition-colors ' + (additional_classes or '')"
                t-att-onclick="onclick_handler"
                t-att-type="button_type or 'button'"
                t-att-disabled="disabled">
            <i t-if="icon" t-att-class="'fas ' + icon + ' mr-2'"></i>
            <span t-esc="button_text or 'Button'"/>
        </button>
    </t>

    <!-- Badge Components -->
    <t t-name="components.badge_success">
        <span t-att-class="'inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 border border-green-200 ' + (additional_classes or '')">
            <i t-if="icon" t-att-class="'fas ' + icon + ' mr-1 text-xs'"></i>
            <span t-esc="badge_text or 'Success'"/>
        </span>
    </t>

    <t t-name="components.badge_warning">
        <span t-att-class="'inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 border border-yellow-200 ' + (additional_classes or '')">
            <i t-if="icon" t-att-class="'fas ' + icon + ' mr-1 text-xs'"></i>
            <span t-esc="badge_text or 'Warning'"/>
        </span>
    </t>

    <t t-name="components.badge_error">
        <span t-att-class="'inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 border border-red-200 ' + (additional_classes or '')">
            <i t-if="icon" t-att-class="'fas ' + icon + ' mr-1 text-xs'"></i>
            <span t-esc="badge_text or 'Error'"/>
        </span>
    </t>

    <t t-name="components.badge_info">
        <span t-att-class="'inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 border border-blue-200 ' + (additional_classes or '')">
            <i t-if="icon" t-att-class="'fas ' + icon + ' mr-1 text-xs'"></i>
            <span t-esc="badge_text or 'Info'"/>
        </span>
    </t>

    <!-- Form Input Components -->
    <t t-name="components.form_input">
        <div t-att-class="'mb-4 ' + (container_classes or '')">
            <label t-if="label" t-att-for="input_id" class="block text-sm font-medium text-gray-700 mb-2">
                <span t-esc="label"/>
                <span t-if="required" class="text-red-500">*</span>
            </label>
            <input t-att-type="input_type or 'text'"
                   t-att-id="input_id"
                   t-att-name="input_name"
                   t-att-value="input_value"
                   t-att-placeholder="placeholder"
                   t-att-required="required"
                   t-att-pattern="pattern"
                   t-att-title="title"
                   t-att-class="'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 transition-colors text-sm ' + (input_classes or '')" />
            <p t-if="help_text" class="mt-1 text-xs text-gray-500" t-esc="help_text"/>
        </div>
    </t>

    <t t-name="components.form_select">
        <div t-att-class="'mb-4 ' + (container_classes or '')">
            <label t-if="label" t-att-for="select_id" class="block text-sm font-medium text-gray-700 mb-2">
                <span t-esc="label"/>
                <span t-if="required" class="text-red-500">*</span>
            </label>
            <select t-att-id="select_id"
                    t-att-name="select_name"
                    t-att-required="required"
                    t-att-class="'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 transition-colors text-sm ' + (select_classes or '')">
                <t t-if="options">
                    <t t-foreach="options" t-as="option">
                        <option t-att-value="option.value" t-att-selected="option.selected" t-esc="option.label"/>
                    </t>
                </t>
                <t t-else="">
                    <t t-slot="select_options"/>
                </t>
            </select>
            <p t-if="help_text" class="mt-1 text-xs text-gray-500" t-esc="help_text"/>
        </div>
    </t>

    <!-- Loading Spinner Component -->
    <t t-name="components.loading_spinner">
        <div t-att-class="'flex items-center justify-center ' + (container_classes or '')">
            <div t-att-class="'relative ' + (size_classes or 'w-8 h-8')">
                <div class="absolute inset-0 border-4 border-gray-200 rounded-full"></div>
                <div t-att-class="'absolute inset-0 border-4 rounded-full border-t-transparent animate-spin ' + (color_classes or 'border-blue-600')"></div>
            </div>
            <span t-if="loading_text" t-att-class="'ml-3 text-sm ' + (text_classes or 'text-gray-600')" t-esc="loading_text"/>
        </div>
    </t>

    <!-- Card Component -->
    <t t-name="components.card">
        <div t-att-class="'bg-white border border-gray-200 rounded-lg shadow-sm ' + (card_classes or '')">
            <div t-if="card_header" t-att-class="'p-4 border-b border-gray-200 ' + (header_classes or '')">
                <t t-raw="card_header"/>
            </div>
            <div t-att-class="'p-4 ' + (body_classes or '')">
                <t t-if="card_content">
                    <t t-raw="card_content"/>
                </t>
                <t t-else="">
                    <t t-slot="card_body"/>
                </t>
            </div>
            <div t-if="card_footer" t-att-class="'p-4 border-t border-gray-200 ' + (footer_classes or '')">
                <t t-raw="card_footer"/>
            </div>
        </div>
    </t>

    <!-- Alert Component -->
    <t t-name="components.alert">
        <div t-att-class="'p-4 rounded-md border ' + (alert_type == 'success' and 'bg-green-50 border-green-200 text-green-800' or alert_type == 'warning' and 'bg-yellow-50 border-yellow-200 text-yellow-800' or alert_type == 'error' and 'bg-red-50 border-red-200 text-red-800' or 'bg-blue-50 border-blue-200 text-blue-800') + ' ' + (alert_classes or '')">
            <div class="flex items-start">
                <div class="flex-shrink-0">
                    <i t-att-class="'fas ' + (alert_type == 'success' and 'fa-check-circle text-green-500' or alert_type == 'warning' and 'fa-exclamation-triangle text-yellow-500' or alert_type == 'error' and 'fa-times-circle text-red-500' or 'fa-info-circle text-blue-500')"></i>
                </div>
                <div class="ml-3 flex-1">
                    <h3 t-if="alert_title" class="text-sm font-medium mb-1" t-esc="alert_title"/>
                    <div class="text-sm" t-esc="alert_message"/>
                </div>
                <div t-if="dismissible" class="ml-auto pl-3">
                    <button type="button" class="text-gray-400 hover:text-gray-600" onclick="this.parentElement.parentElement.parentElement.remove()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        </div>
    </t>
</templates>
